/*
 * 视频轮播脚本
 * 功能：使用AVPro MediaPlayer组件实现视频的循环播放
 * 使用方法：将此脚本挂载到包含MediaPlayer组件的GameObject上
 */

using UnityEngine;
using RenderHeads.Media.AVProVideo;

namespace VideoPlayback
{
    /// <summary>
    /// 视频轮播控制器
    /// 负责控制MediaPlayer组件进行视频的循环播放
    /// </summary>
    public class VideoLooper : MonoBehaviour
    {
        [Header("视频轮播设置")]
        [SerializeField]
        [Tooltip("MediaPlayer组件引用")]
        private MediaPlayer mediaPlayer;

        [SerializeField]
        [Tooltip("是否在启动时自动开始播放")]
        private bool autoPlayOnStart = true;

        [SerializeField]
        [Tooltip("视频结束后重新开始播放的延迟时间（秒）")]
        private float loopDelay = 0f;

        private bool isLooping = false;

        void Start()
        {
            // 如果没有手动指定MediaPlayer，尝试从当前GameObject获取
            if (mediaPlayer == null)
            {
                mediaPlayer = GetComponent<MediaPlayer>();
            }

            // 检查MediaPlayer组件是否存在
            if (mediaPlayer == null)
            {
                Debug.LogError("[VideoLooper] 未找到MediaPlayer组件！请确保GameObject上有MediaPlayer组件或手动指定引用。");
                return;
            }

            // 订阅视频播放完成事件
            if (mediaPlayer.Events != null)
            {
                mediaPlayer.Events.AddListener(OnMediaPlayerEvent);
            }

            // 自动开始播放
            if (autoPlayOnStart)
            {
                StartLooping();
            }
        }

        void OnDestroy()
        {
            // 取消事件订阅
            if (mediaPlayer != null && mediaPlayer.Events != null)
            {
                mediaPlayer.Events.RemoveListener(OnMediaPlayerEvent);
            }
        }

        /// <summary>
        /// 开始视频轮播
        /// </summary>
        public void StartLooping()
        {
            if (mediaPlayer == null)
            {
                Debug.LogError("[VideoLooper] MediaPlayer组件为空，无法开始轮播。");
                return;
            }

            isLooping = true;
            
            // 如果视频已经加载，直接播放
            if (mediaPlayer.Info != null && mediaPlayer.Info.HasVideo())
            {
                mediaPlayer.Play();
            }
            else
            {
                Debug.LogWarning("[VideoLooper] 视频尚未加载完成，等待加载完成后自动播放。");
            }
        }

        /// <summary>
        /// 停止视频轮播
        /// </summary>
        public void StopLooping()
        {
            isLooping = false;
            
            if (mediaPlayer != null)
            {
                mediaPlayer.Stop();
            }
        }

        /// <summary>
        /// 暂停视频播放
        /// </summary>
        public void PauseLooping()
        {
            if (mediaPlayer != null)
            {
                mediaPlayer.Pause();
            }
        }

        /// <summary>
        /// 恢复视频播放
        /// </summary>
        public void ResumeLooping()
        {
            if (mediaPlayer != null && isLooping)
            {
                mediaPlayer.Play();
            }
        }

        /// <summary>
        /// 处理MediaPlayer事件
        /// </summary>
        /// <param name="player">MediaPlayer实例</param>
        /// <param name="eventType">事件类型</param>
        /// <param name="errorCode">错误代码</param>
        private void OnMediaPlayerEvent(MediaPlayer player, MediaPlayerEvent.EventType eventType, ErrorCode errorCode)
        {
            switch (eventType)
            {
                case MediaPlayerEvent.EventType.FinishedPlaying:
                    // 视频播放完成，如果正在轮播则重新开始
                    if (isLooping)
                    {
                        RestartVideo();
                    }
                    break;

                case MediaPlayerEvent.EventType.Error:
                    Debug.LogError($"[VideoLooper] MediaPlayer发生错误: {errorCode}");
                    break;

                case MediaPlayerEvent.EventType.ReadyToPlay:
                    // 视频准备就绪，如果设置了自动播放则开始播放
                    if (isLooping && autoPlayOnStart)
                    {
                        mediaPlayer.Play();
                    }
                    break;
            }
        }

        /// <summary>
        /// 重新开始播放视频
        /// </summary>
        private void RestartVideo()
        {
            if (loopDelay > 0f)
            {
                // 有延迟时间，使用协程延迟重启
                StartCoroutine(RestartVideoWithDelay());
            }
            else
            {
                // 无延迟，直接重启
                mediaPlayer.Rewind(false);
                mediaPlayer.Play();
            }
        }

        /// <summary>
        /// 延迟重启视频的协程
        /// </summary>
        private System.Collections.IEnumerator RestartVideoWithDelay()
        {
            yield return new WaitForSeconds(loopDelay);
            
            if (isLooping && mediaPlayer != null)
            {
                mediaPlayer.Rewind(false);
                mediaPlayer.Play();
            }
        }

        /// <summary>
        /// 设置轮播延迟时间
        /// </summary>
        /// <param name="delay">延迟时间（秒）</param>
        public void SetLoopDelay(float delay)
        {
            loopDelay = Mathf.Max(0f, delay);
        }

        /// <summary>
        /// 获取当前是否正在轮播
        /// </summary>
        /// <returns>是否正在轮播</returns>
        public bool IsLooping()
        {
            return isLooping;
        }

        /// <summary>
        /// 获取MediaPlayer是否正在播放
        /// </summary>
        /// <returns>是否正在播放</returns>
        public bool IsPlaying()
        {
            return mediaPlayer != null && mediaPlayer.Control != null && mediaPlayer.Control.IsPlaying();
        }
    }
}
