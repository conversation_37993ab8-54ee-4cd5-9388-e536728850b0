/*
 * 视频显示脚本
 * 功能：将MediaPlayer的视频显示到RawImage上
 */

using UnityEngine;
using UnityEngine.UI;
using RenderHeads.Media.AVProVideo;

public class VideoDisplay : MonoBehaviour
{
    [SerializeField] private MediaPlayer mediaPlayer;
    [SerializeField] private RawImage displayImage;

    void Start()
    {
        if (mediaPlayer == null)
            mediaPlayer = GetComponent<MediaPlayer>();

        if (displayImage == null)
            displayImage = GetComponent<RawImage>();
    }

    void Update()
    {
        if (mediaPlayer != null && displayImage != null && mediaPlayer.TextureProducer != null)
        {
            Texture videoTexture = mediaPlayer.TextureProducer.GetTexture();
            if (videoTexture != null)
            {
                displayImage.texture = videoTexture;
            }
        }
    }
}
}
