/*
 * 视频播放管理器
 * 功能：整合视频轮播和显示功能的完整解决方案
 * 使用方法：将此脚本挂载到GameObject上，设置MediaPlayer和RawImage引用，配置视频路径
 */

using UnityEngine;
using UnityEngine.UI;
using RenderHeads.Media.AVProVideo;

namespace VideoPlayback
{
    /// <summary>
    /// 视频播放管理器
    /// 整合视频加载、轮播和显示功能
    /// </summary>
    public class VideoPlaybackManager : MonoBehaviour
    {
        [Header("组件引用")]
        [SerializeField]
        [Tooltip("MediaPlayer组件引用")]
        private MediaPlayer mediaPlayer;

        [SerializeField]
        [Tooltip("用于显示视频的RawImage组件")]
        private RawImage displayImage;

        [Header("视频设置")]
        [SerializeField]
        [Tooltip("视频文件路径（相对于StreamingAssets文件夹）")]
        private string videoPath = "AVProVideoSamples/BigBuckBunny-360p30-H264.mp4";

        [SerializeField]
        [Tooltip("是否在启动时自动播放")]
        private bool autoPlayOnStart = true;

        [SerializeField]
        [Tooltip("是否循环播放")]
        private bool loopVideo = true;

        [SerializeField]
        [Tooltip("循环播放的延迟时间（秒）")]
        private float loopDelay = 0f;

        [Header("显示设置")]
        [SerializeField]
        [Tooltip("视频未准备好时显示的默认纹理")]
        private Texture2D defaultTexture;

        [SerializeField]
        [Tooltip("是否保持视频的宽高比")]
        private bool maintainAspectRatio = true;

        [SerializeField]
        [Tooltip("视频缩放模式")]
        private ScaleMode scaleMode = ScaleMode.ScaleToFit;

        // 私有变量
        private bool isInitialized = false;
        private bool isLooping = false;
        private bool isVideoReady = false;

        void Start()
        {
            InitializeComponents();
            if (autoPlayOnStart)
            {
                LoadAndPlayVideo();
            }
        }

        void Update()
        {
            if (isInitialized)
            {
                UpdateVideoDisplay();
            }
        }

        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponents()
        {
            // 自动查找组件
            if (mediaPlayer == null)
            {
                mediaPlayer = GetComponent<MediaPlayer>();
                if (mediaPlayer == null)
                {
                    mediaPlayer = FindObjectOfType<MediaPlayer>();
                }
            }

            if (displayImage == null)
            {
                displayImage = GetComponent<RawImage>();
                if (displayImage == null)
                {
                    displayImage = FindObjectOfType<RawImage>();
                }
            }

            // 检查必要组件
            if (mediaPlayer == null)
            {
                Debug.LogError("[VideoPlaybackManager] 未找到MediaPlayer组件！");
                return;
            }

            if (displayImage == null)
            {
                Debug.LogError("[VideoPlaybackManager] 未找到RawImage组件！");
                return;
            }

            // 设置默认纹理
            if (defaultTexture != null)
            {
                displayImage.texture = defaultTexture;
            }

            // 订阅MediaPlayer事件
            if (mediaPlayer.Events != null)
            {
                mediaPlayer.Events.AddListener(OnMediaPlayerEvent);
            }

            isInitialized = true;
            Debug.Log("[VideoPlaybackManager] 视频播放管理器初始化完成。");
        }

        /// <summary>
        /// 加载并播放视频
        /// </summary>
        public void LoadAndPlayVideo()
        {
            if (!isInitialized || string.IsNullOrEmpty(videoPath))
            {
                Debug.LogError("[VideoPlaybackManager] 无法加载视频：管理器未初始化或视频路径为空。");
                return;
            }

            Debug.Log($"[VideoPlaybackManager] 开始加载视频: {videoPath}");
            
            // 设置视频路径并打开
            mediaPlayer.MediaPath.PathType = MediaPathType.RelativeToStreamingAssetsFolder;
            mediaPlayer.MediaPath.Path = videoPath;
            mediaPlayer.OpenMedia();

            isLooping = loopVideo;
        }

        /// <summary>
        /// 更新视频显示
        /// </summary>
        private void UpdateVideoDisplay()
        {
            if (mediaPlayer.TextureProducer != null)
            {
                Texture videoTexture = mediaPlayer.TextureProducer.GetTexture();
                
                if (videoTexture != null)
                {
                    if (displayImage.texture != videoTexture)
                    {
                        displayImage.texture = videoTexture;
                        
                        if (maintainAspectRatio)
                        {
                            UpdateAspectRatio(videoTexture);
                        }
                    }
                }
                else if (displayImage.texture != defaultTexture)
                {
                    displayImage.texture = defaultTexture;
                }
            }
        }

        /// <summary>
        /// 更新宽高比设置
        /// </summary>
        private void UpdateAspectRatio(Texture videoTexture)
        {
            if (videoTexture == null || displayImage == null) return;

            float videoAspect = (float)videoTexture.width / videoTexture.height;
            RectTransform rectTransform = displayImage.rectTransform;
            float containerAspect = rectTransform.rect.width / rectTransform.rect.height;

            switch (scaleMode)
            {
                case ScaleMode.ScaleToFit:
                    if (videoAspect > containerAspect)
                    {
                        float height = 1f / videoAspect * containerAspect;
                        displayImage.uvRect = new Rect(0, (1f - height) * 0.5f, 1f, height);
                    }
                    else
                    {
                        float width = videoAspect / containerAspect;
                        displayImage.uvRect = new Rect((1f - width) * 0.5f, 0, width, 1f);
                    }
                    break;

                case ScaleMode.ScaleAndCrop:
                    if (videoAspect > containerAspect)
                    {
                        float width = containerAspect / videoAspect;
                        displayImage.uvRect = new Rect((1f - width) * 0.5f, 0, width, 1f);
                    }
                    else
                    {
                        float height = videoAspect / containerAspect;
                        displayImage.uvRect = new Rect(0, (1f - height) * 0.5f, 1f, height);
                    }
                    break;

                case ScaleMode.StretchToFill:
                default:
                    displayImage.uvRect = new Rect(0, 0, 1f, 1f);
                    break;
            }
        }

        /// <summary>
        /// 处理MediaPlayer事件
        /// </summary>
        private void OnMediaPlayerEvent(MediaPlayer player, MediaPlayerEvent.EventType eventType, ErrorCode errorCode)
        {
            switch (eventType)
            {
                case MediaPlayerEvent.EventType.ReadyToPlay:
                    Debug.Log("[VideoPlaybackManager] 视频准备就绪，开始播放。");
                    isVideoReady = true;
                    mediaPlayer.Play();
                    break;

                case MediaPlayerEvent.EventType.FinishedPlaying:
                    Debug.Log("[VideoPlaybackManager] 视频播放完成。");
                    if (isLooping)
                    {
                        RestartVideo();
                    }
                    break;

                case MediaPlayerEvent.EventType.Error:
                    Debug.LogError($"[VideoPlaybackManager] MediaPlayer发生错误: {errorCode}");
                    break;
            }
        }

        /// <summary>
        /// 重新开始播放视频
        /// </summary>
        private void RestartVideo()
        {
            if (loopDelay > 0f)
            {
                StartCoroutine(RestartVideoWithDelay());
            }
            else
            {
                mediaPlayer.Rewind(false);
                mediaPlayer.Play();
            }
        }

        /// <summary>
        /// 延迟重启视频
        /// </summary>
        private System.Collections.IEnumerator RestartVideoWithDelay()
        {
            yield return new WaitForSeconds(loopDelay);
            
            if (isLooping && mediaPlayer != null)
            {
                mediaPlayer.Rewind(false);
                mediaPlayer.Play();
            }
        }

        /// <summary>
        /// 开始播放
        /// </summary>
        public void Play()
        {
            if (isVideoReady)
            {
                mediaPlayer.Play();
            }
            else
            {
                LoadAndPlayVideo();
            }
        }

        /// <summary>
        /// 暂停播放
        /// </summary>
        public void Pause()
        {
            if (mediaPlayer != null)
            {
                mediaPlayer.Pause();
            }
        }

        /// <summary>
        /// 停止播放
        /// </summary>
        public void Stop()
        {
            isLooping = false;
            if (mediaPlayer != null)
            {
                mediaPlayer.Stop();
            }
        }

        /// <summary>
        /// 设置视频路径
        /// </summary>
        public void SetVideoPath(string path)
        {
            videoPath = path;
        }

        /// <summary>
        /// 设置是否循环播放
        /// </summary>
        public void SetLooping(bool loop)
        {
            isLooping = loop;
        }

        void OnDestroy()
        {
            if (mediaPlayer != null && mediaPlayer.Events != null)
            {
                mediaPlayer.Events.RemoveListener(OnMediaPlayerEvent);
            }
        }
    }
}
