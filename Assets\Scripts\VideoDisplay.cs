/*
 * 视频显示脚本
 * 功能：将AVPro MediaPlayer的视频内容显示到UI组件上
 * 使用方法：将此脚本挂载到包含RawImage组件的GameObject上，并设置MediaPlayer引用
 */

using UnityEngine;
using UnityEngine.UI;
using RenderHeads.Media.AVProVideo;

namespace VideoPlayback
{
    /// <summary>
    /// 视频显示控制器
    /// 负责将MediaPlayer的视频内容显示到UI组件上
    /// </summary>
    public class VideoDisplay : MonoBehaviour
    {
        [Header("视频显示设置")]
        [SerializeField]
        [Tooltip("MediaPlayer组件引用")]
        private MediaPlayer mediaPlayer;

        [SerializeField]
        [Tooltip("用于显示视频的RawImage组件")]
        private RawImage displayImage;

        [SerializeField]
        [Tooltip("视频未准备好时显示的默认纹理")]
        private Texture2D defaultTexture;

        [SerializeField]
        [Tooltip("是否保持视频的宽高比")]
        private bool maintainAspectRatio = true;

        [SerializeField]
        [Tooltip("视频缩放模式")]
        private ScaleMode scaleMode = ScaleMode.ScaleToFit;

        private bool isInitialized = false;

        void Start()
        {
            InitializeComponents();
        }

        void Update()
        {
            if (isInitialized)
            {
                UpdateVideoDisplay();
            }
        }

        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponents()
        {
            // 如果没有手动指定MediaPlayer，尝试从当前GameObject或父对象获取
            if (mediaPlayer == null)
            {
                mediaPlayer = GetComponent<MediaPlayer>();
                if (mediaPlayer == null)
                {
                    mediaPlayer = GetComponentInParent<MediaPlayer>();
                }
            }

            // 如果没有手动指定RawImage，尝试从当前GameObject获取
            if (displayImage == null)
            {
                displayImage = GetComponent<RawImage>();
            }

            // 检查必要组件
            if (mediaPlayer == null)
            {
                Debug.LogError("[VideoDisplay] 未找到MediaPlayer组件！请确保设置了MediaPlayer引用。");
                return;
            }

            if (displayImage == null)
            {
                Debug.LogError("[VideoDisplay] 未找到RawImage组件！请确保GameObject上有RawImage组件或手动指定引用。");
                return;
            }

            // 设置默认纹理
            if (defaultTexture != null)
            {
                displayImage.texture = defaultTexture;
            }

            isInitialized = true;
            Debug.Log("[VideoDisplay] 视频显示组件初始化完成。");
        }

        /// <summary>
        /// 更新视频显示
        /// </summary>
        private void UpdateVideoDisplay()
        {
            if (mediaPlayer.TextureProducer != null)
            {
                Texture videoTexture = mediaPlayer.TextureProducer.GetTexture();
                
                if (videoTexture != null)
                {
                    // 更新显示纹理
                    if (displayImage.texture != videoTexture)
                    {
                        displayImage.texture = videoTexture;
                        
                        // 如果需要保持宽高比，调整RawImage的UV Rect
                        if (maintainAspectRatio)
                        {
                            UpdateAspectRatio(videoTexture);
                        }
                    }
                }
                else if (displayImage.texture != defaultTexture)
                {
                    // 视频纹理不可用时显示默认纹理
                    displayImage.texture = defaultTexture;
                }
            }
            else if (displayImage.texture != defaultTexture)
            {
                // TextureProducer不可用时显示默认纹理
                displayImage.texture = defaultTexture;
            }
        }

        /// <summary>
        /// 更新宽高比设置
        /// </summary>
        /// <param name="videoTexture">视频纹理</param>
        private void UpdateAspectRatio(Texture videoTexture)
        {
            if (videoTexture == null || displayImage == null) return;

            float videoAspect = (float)videoTexture.width / videoTexture.height;
            RectTransform rectTransform = displayImage.rectTransform;
            float containerAspect = rectTransform.rect.width / rectTransform.rect.height;

            switch (scaleMode)
            {
                case ScaleMode.ScaleToFit:
                    // 缩放以适应容器，保持宽高比
                    if (videoAspect > containerAspect)
                    {
                        // 视频更宽，以宽度为准
                        float height = 1f / videoAspect * containerAspect;
                        displayImage.uvRect = new Rect(0, (1f - height) * 0.5f, 1f, height);
                    }
                    else
                    {
                        // 视频更高，以高度为准
                        float width = videoAspect / containerAspect;
                        displayImage.uvRect = new Rect((1f - width) * 0.5f, 0, width, 1f);
                    }
                    break;

                case ScaleMode.ScaleAndCrop:
                    // 缩放以填满容器，可能裁剪
                    if (videoAspect > containerAspect)
                    {
                        // 视频更宽，裁剪左右
                        float width = containerAspect / videoAspect;
                        displayImage.uvRect = new Rect((1f - width) * 0.5f, 0, width, 1f);
                    }
                    else
                    {
                        // 视频更高，裁剪上下
                        float height = videoAspect / containerAspect;
                        displayImage.uvRect = new Rect(0, (1f - height) * 0.5f, 1f, height);
                    }
                    break;

                case ScaleMode.StretchToFill:
                default:
                    // 拉伸填满，不保持宽高比
                    displayImage.uvRect = new Rect(0, 0, 1f, 1f);
                    break;
            }
        }

        /// <summary>
        /// 设置MediaPlayer引用
        /// </summary>
        /// <param name="player">MediaPlayer实例</param>
        public void SetMediaPlayer(MediaPlayer player)
        {
            mediaPlayer = player;
            if (isInitialized)
            {
                Debug.Log("[VideoDisplay] MediaPlayer引用已更新。");
            }
        }

        /// <summary>
        /// 设置显示图像组件
        /// </summary>
        /// <param name="image">RawImage组件</param>
        public void SetDisplayImage(RawImage image)
        {
            displayImage = image;
            if (isInitialized)
            {
                Debug.Log("[VideoDisplay] RawImage引用已更新。");
            }
        }

        /// <summary>
        /// 设置默认纹理
        /// </summary>
        /// <param name="texture">默认纹理</param>
        public void SetDefaultTexture(Texture2D texture)
        {
            defaultTexture = texture;
            if (displayImage != null && displayImage.texture == null)
            {
                displayImage.texture = defaultTexture;
            }
        }

        /// <summary>
        /// 设置是否保持宽高比
        /// </summary>
        /// <param name="maintain">是否保持宽高比</param>
        public void SetMaintainAspectRatio(bool maintain)
        {
            maintainAspectRatio = maintain;
        }

        /// <summary>
        /// 设置缩放模式
        /// </summary>
        /// <param name="mode">缩放模式</param>
        public void SetScaleMode(ScaleMode mode)
        {
            scaleMode = mode;
        }
    }
}
